<template>
  <div class="meet-page">
    <v-container fluid class="pa-4">
      <v-row>
        <v-col cols="12">
          <div class="meet-header mb-4">
            <h1 class="text-h4 font-weight-bold mb-2">Video Call Test</h1>
            <p class="text-subtitle-1 grey--text">
              Test the Whereby video call integration
            </p>
          </div>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" md="8">
          <v-card class="meet-video-card" elevation="2">
            <v-card-title class="d-flex align-center justify-space-between">
              <span>Video Call</span>
              <div class="video-provider-buttons">
                <v-btn
                  :class="['mr-2', { primary: currentProvider === 'twilio' }]"
                  small
                  @click="switchProvider('twilio')"
                >
                  A - Twilio
                </v-btn>
                <v-btn
                  :class="['mr-2', { primary: currentProvider === 'tokbox' }]"
                  small
                  @click="switchProvider('tokbox')"
                >
                  B - Tokbox
                </v-btn>
                <v-btn
                  :class="[{ primary: currentProvider === 'whereby' }]"
                  small
                  @click="switchProvider('whereby')"
                >
                  C - Whereby
                </v-btn>
              </div>
            </v-card-title>
            <v-card-text class="pa-0">
              <div
                class="video-container"
                style="height: 500px; position: relative"
              >
                <!-- Whereby Video Component -->
                <div
                  v-if="currentProvider === 'whereby'"
                  id="whereby-video-container"
                  class="whereby-video-container"
                  style="width: 100%; height: 100%"
                ></div>

                <!-- Placeholder for other providers -->
                <div
                  v-else
                  class="d-flex align-center justify-center"
                  style="height: 100%; background: #f5f5f5"
                >
                  <div class="text-center">
                    <v-icon size="64" color="grey">mdi-video-off</v-icon>
                    <p class="text-h6 mt-2 grey--text">
                      {{ currentProvider === 'twilio' ? 'Twilio' : 'Tokbox' }}
                      Provider Selected
                    </p>
                    <p class="grey--text">
                      Video integration not implemented for testing
                    </p>
                  </div>
                </div>

                <!-- Video Controls -->
                <div
                  v-if="currentProvider === 'whereby'"
                  class="video-controls"
                  style="
                    position: absolute;
                    bottom: 16px;
                    left: 16px;
                    right: 16px;
                  "
                >
                  <v-card class="pa-2" style="background: rgba(0, 0, 0, 0.8)">
                    <div class="d-flex align-center justify-space-between">
                      <!-- Left side controls -->
                      <div class="d-flex align-center">
                        <!-- Video toggle -->
                        <v-btn
                          :color="isVideoEnabled ? 'success' : 'error'"
                          icon
                          small
                          @click="toggleVideo"
                        >
                          <v-icon>{{
                            isVideoEnabled ? 'mdi-video' : 'mdi-video-off'
                          }}</v-icon>
                        </v-btn>

                        <!-- Audio toggle -->
                        <v-btn
                          :color="!isMuted ? 'success' : 'error'"
                          icon
                          small
                          class="mx-1"
                          @click="toggleAudio"
                        >
                          <v-icon>{{
                            !isMuted ? 'mdi-microphone' : 'mdi-microphone-off'
                          }}</v-icon>
                        </v-btn>

                        <!-- Screen share -->
                        <v-btn
                          :color="isScreenShareEnabled ? 'primary' : 'default'"
                          icon
                          small
                          class="mx-1"
                          @click="toggleScreenShare"
                        >
                          <v-icon>mdi-monitor-share</v-icon>
                        </v-btn>

                        <!-- Hand raise -->
                        <v-btn
                          :color="isHandRaised ? 'warning' : 'default'"
                          icon
                          small
                          class="mx-1"
                          @click="toggleHandRaise"
                        >
                          <v-icon>mdi-hand-back-right</v-icon>
                        </v-btn>
                      </div>

                      <!-- Center - Reactions -->
                      <div class="d-flex align-center">
                        <v-btn
                          icon
                          small
                          class="mx-1"
                          @click="sendReaction('👍')"
                        >
                          <span style="font-size: 16px">👍</span>
                        </v-btn>
                        <v-btn
                          icon
                          small
                          class="mx-1"
                          @click="sendReaction('👏')"
                        >
                          <span style="font-size: 16px">👏</span>
                        </v-btn>
                        <v-btn
                          icon
                          small
                          class="mx-1"
                          @click="sendReaction('❤️')"
                        >
                          <span style="font-size: 16px">❤️</span>
                        </v-btn>
                        <v-btn
                          icon
                          small
                          class="mx-1"
                          @click="sendReaction('😂')"
                        >
                          <span style="font-size: 16px">😂</span>
                        </v-btn>
                      </div>

                      <!-- Right side controls -->
                      <div class="d-flex align-center">
                        <!-- Chat toggle -->
                        <v-btn
                          :color="isChatEnabled ? 'primary' : 'default'"
                          icon
                          small
                          class="mx-1"
                          @click="toggleChat"
                        >
                          <v-icon>mdi-chat</v-icon>
                        </v-btn>

                        <!-- Participants -->
                        <v-btn
                          :color="isParticipantsEnabled ? 'primary' : 'default'"
                          icon
                          small
                          class="mx-1"
                          @click="toggleParticipants"
                        >
                          <v-icon>mdi-account-group</v-icon>
                        </v-btn>

                        <!-- Fullscreen -->
                        <v-btn
                          color="primary"
                          icon
                          small
                          class="mx-1"
                          @click="toggleFullScreen"
                        >
                          <v-icon>mdi-fullscreen</v-icon>
                        </v-btn>

                        <!-- Host/Participant role indicator -->
                        <v-chip
                          :color="
                            currentRole === 'host' ? 'success' : 'primary'
                          "
                          small
                          text-color="white"
                          class="ml-2"
                        >
                          {{ currentRole === 'host' ? 'Host' : 'Participant' }}
                        </v-chip>
                      </div>
                    </div>
                  </v-card>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="4">
          <v-card elevation="2">
            <v-card-title>Connection Info</v-card-title>
            <v-card-text>
              <div class="mb-3">
                <strong>Provider:</strong> {{ currentProvider.toUpperCase() }}
              </div>
              <div class="mb-3">
                <strong>Status:</strong>
                <v-chip
                  :color="isConnected ? 'success' : 'warning'"
                  small
                  text-color="white"
                >
                  {{ isConnected ? 'Connected' : 'Connecting...' }}
                </v-chip>
              </div>
              <div class="mb-3">
                <strong>Role:</strong>
                <v-chip
                  :color="currentRole === 'host' ? 'success' : 'primary'"
                  small
                  text-color="white"
                >
                  {{ currentRole === 'host' ? 'Host' : 'Participant' }}
                </v-chip>
              </div>
              <div class="mb-3">
                <strong>Room:</strong> rwrre2ae7c7d2-8906-4ceb...
              </div>
              <div class="mb-3">
                <strong>Video:</strong> {{ isVideoEnabled ? 'On' : 'Off' }}
              </div>
              <div class="mb-3">
                <strong>Audio:</strong> {{ !isMuted ? 'On' : 'Off' }}
              </div>
              <div class="mb-3">
                <strong>Screen Share:</strong>
                {{ isScreenShareEnabled ? 'On' : 'Off' }}
              </div>
              <div class="mb-3">
                <strong>Hand Raised:</strong> {{ isHandRaised ? 'Yes' : 'No' }}
              </div>
              <div class="mb-3">
                <strong>Chat:</strong>
                {{ isChatEnabled ? 'Enabled' : 'Disabled' }}
              </div>
              <div class="mb-3">
                <strong>Participants:</strong>
                {{ isParticipantsEnabled ? 'Visible' : 'Hidden' }}
              </div>
            </v-card-text>
          </v-card>

          <v-card class="mt-4" elevation="2">
            <v-card-title>Instructions</v-card-title>
            <v-card-text>
              <ol class="pl-4">
                <li>
                  Click on provider buttons (A, B, C) to switch between video
                  providers
                </li>
                <li>
                  Whereby (C) includes all paid features: reactions, hand
                  raising, screen sharing, chat, etc.
                </li>
                <li>
                  Choose "Host" for full control or "Participant" for standard
                  access
                </li>
                <li>
                  Use the embedded interface with all Whereby features enabled
                </li>
                <li>
                  Test reactions (👍👏❤️😂), hand raising, screen sharing, and
                  chat
                </li>
                <li>Multiple users can join the same room simultaneously</li>
                <li>Host has additional controls and moderator features</li>
              </ol>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
export default {
  name: 'MeetPage',
  data() {
    return {
      currentProvider: 'whereby',
      isConnected: false,
      isVideoEnabled: true,
      isMuted: false,
      roomName: 'test-meet-room',
      wherebyClient: null,
      localStreamContainer: null,
      // Whereby paid features
      isScreenShareEnabled: false,
      isHandRaised: false,
      isChatEnabled: false,
      isParticipantsEnabled: false,
      currentRole: 'participant', // 'host' or 'participant'
      reactions: [],
    }
  },
  head() {
    return {
      title: 'Video Call Test - Langu',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Test video call functionality with Whereby integration',
        },
      ],
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.localStreamContainer = document.getElementById(
        'whereby-video-container'
      )
      if (this.currentProvider === 'whereby') {
        this.initializeWhereby()
      }
    })

    window.addEventListener('beforeunload', this.cleanup)
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    switchProvider(provider) {
      this.cleanup()
      this.currentProvider = provider
      this.isConnected = false

      if (provider === 'whereby') {
        this.$nextTick(() => {
          this.localStreamContainer = document.getElementById(
            'whereby-video-container'
          )
          this.initializeWhereby()
        })
      }
    },
    initializeWhereby() {
      try {
        // For CSP compliance, we need to open Whereby in a new window instead of iframe
        // The CSP error indicates that Whereby doesn't allow embedding from this domain

        // Create embedded iframe interface like existing Whereby component
        const container = this.localStreamContainer
        if (container) {
          // Create role selection interface first
          container.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center;">
              <div style="margin-bottom: 20px;">
                <h3 style="color: #333; margin-bottom: 10px;">Whereby Video Call</h3>
                <p style="color: #666; margin-bottom: 20px;">Choose your role to join the embedded video room</p>
              </div>

              <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                <button
                  id="whereby-host-btn"
                  style="
                    background: #28a745;
                    color: white;
                    border: none;
                    padding: 12px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    cursor: pointer;
                    font-weight: 600;
                  "
                  onmouseover="this.style.background='#218838'"
                  onmouseout="this.style.background='#28a745'"
                >
                  Open as Host (New Window)
                </button>

                <button
                  id="whereby-participant-btn"
                  style="
                    background: #5E72E4;
                    color: white;
                    border: none;
                    padding: 12px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    cursor: pointer;
                    font-weight: 600;
                  "
                  onmouseover="this.style.background='#4C63D2'"
                  onmouseout="this.style.background='#5E72E4'"
                >
                  Open as Participant (New Window)
                </button>
              </div>

              <p style="color: #888; font-size: 14px;">Room: rwrre2ae7c7d2-8906-4ceb-90fd-f4571e5e75e6</p>
              <p style="color: #999; font-size: 12px;">Embedded interface with all paid features</p>
            </div>
          `

          // Add click handlers for role selection
          const hostBtn = container.querySelector('#whereby-host-btn')
          const participantBtn = container.querySelector(
            '#whereby-participant-btn'
          )

          if (hostBtn && participantBtn) {
            // Host button click handler
            hostBtn.addEventListener('click', () => {
              this.currentRole = 'host'
              this.openWherebyInNewWindow('host')
            })

            // Participant button click handler
            participantBtn.addEventListener('click', () => {
              this.currentRole = 'participant'
              this.openWherebyInNewWindow('participant')
            })
          }
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to initialize Whereby:', error)
        this.isConnected = false
      }
    },

    openWherebyInNewWindow(role) {
      try {
        const container = this.localStreamContainer
        if (!container) return

        // Determine the URL based on role
        let roomUrl
        if (role === 'host') {
          roomUrl =
            'https://langu.whereby.com/rwrre2ae7c7d2-8906-4ceb-90fd-f4571e5e75e6?roomKey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************.u1vYAr2HS_axqTBsyHAvdoztlZHcL09-9_9gkf4b548'
        } else {
          roomUrl =
            'https://langu.whereby.com/rwrre2ae7c7d2-8906-4ceb-90fd-f4571e5e75e6'
        }

        // Build embed parameters with all paid features enabled
        const embedParams = new URLSearchParams({
          displayName: role === 'host' ? 'Test Host' : 'Test Participant',
          audio: !this.isMuted ? 'on' : 'off',
          video: this.isVideoEnabled ? 'on' : 'off',
          // Enable all paid features
          chat: 'on', // Enable chat
          people: 'on', // Enable participants panel
          screenshare: 'on', // Enable screen sharing
          reactions: 'on', // Enable reactions
          handRaise: 'on', // Enable hand raising
          leaveButton: 'on', // Show leave button
          background: 'on', // Enable background effects
          recording: 'on', // Enable recording (if available)
          breakoutRooms: 'on', // Enable breakout rooms (if available)
          whiteboard: 'on', // Enable whiteboard (if available)
          minimal: 'false', // Full interface
        })

        // Determine if we need to add & or ? for parameters
        const separator = roomUrl.includes('?') ? '&' : '?'
        const fullUrl = `${roomUrl}${separator}${embedParams.toString()}`

        // Open in new window with appropriate dimensions
        const windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
        const newWindow = window.open(fullUrl, 'whereby-meet', windowFeatures)

        if (newWindow) {
          // Focus the new window
          newWindow.focus()

          // Update the container to show it's opened
          container.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">
              <div style="margin-bottom: 20px;">
                <h3 style="color: #333; margin-bottom: 10px;">Whereby Video Call Opened</h3>
                <p style="color: #666; margin-bottom: 20px;">Video call opened in new window as ${role}</p>
              </div>
              <p style="color: #888; font-size: 14px;">Room: rwrre2ae7c7d2-8906-4ceb-90fd-f4571e5e75e6</p>
              <p style="color: #999; font-size: 12px;">All paid features enabled</p>
            </div>
          `

          // Mark as connected
          this.isConnected = true

          // Enable features based on role
          if (role === 'host') {
            this.isChatEnabled = true
            this.isParticipantsEnabled = true
          }
        } else {
          // eslint-disable-next-line no-console
          console.error('Failed to open Whereby window - popup blocked?')
          alert('Please allow popups for this site to open the video call')
          this.isConnected = false
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to open Whereby in new window:', error)
        this.isConnected = false
      }
    },

    toggleScreenShare() {
      this.isScreenShareEnabled = !this.isScreenShareEnabled
      // eslint-disable-next-line no-console
      console.log('Screen share toggled:', this.isScreenShareEnabled)
      // Note: Actual screen sharing is handled by Whereby iframe
    },

    toggleHandRaise() {
      this.isHandRaised = !this.isHandRaised
      // eslint-disable-next-line no-console
      console.log('Hand raise toggled:', this.isHandRaised)
      // Note: Actual hand raising is handled by Whereby iframe
    },

    toggleChat() {
      this.isChatEnabled = !this.isChatEnabled
      // eslint-disable-next-line no-console
      console.log('Chat toggled:', this.isChatEnabled)
      // Note: Actual chat toggle is handled by Whereby iframe
    },

    toggleParticipants() {
      this.isParticipantsEnabled = !this.isParticipantsEnabled
      // eslint-disable-next-line no-console
      console.log('Participants panel toggled:', this.isParticipantsEnabled)
      // Note: Actual participants panel is handled by Whereby iframe
    },

    sendReaction(emoji) {
      this.reactions.push({
        emoji,
        timestamp: Date.now(),
      })
      // eslint-disable-next-line no-console
      console.log('Reaction sent:', emoji)
      // Note: Actual reactions are handled by Whereby iframe

      // Remove reaction after 3 seconds
      setTimeout(() => {
        this.reactions = this.reactions.filter(
          (r) => Date.now() - r.timestamp < 3000
        )
      }, 3000)
    },

    toggleVideo() {
      this.isVideoEnabled = !this.isVideoEnabled
      // Note: With iframe approach, video controls are handled within Whereby interface
      // eslint-disable-next-line no-console
      console.log('Video toggled:', this.isVideoEnabled)
    },
    toggleAudio() {
      this.isMuted = !this.isMuted
      // Note: With iframe approach, audio controls are handled within Whereby interface
      // eslint-disable-next-line no-console
      console.log('Audio toggled:', !this.isMuted)
    },
    toggleFullScreen() {
      if (this.localStreamContainer) {
        if (document.fullscreenElement) {
          document.exitFullscreen()
        } else {
          this.localStreamContainer.requestFullscreen()
        }
      }
    },
    cleanup() {
      if (this.localStreamContainer) {
        this.localStreamContainer.innerHTML = ''
      }
      this.isConnected = false
    },
  },
}
</script>

<style lang="scss" scoped>
.meet-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.meet-header {
  text-align: center;
}

.meet-video-card {
  .video-container {
    background: #000;
    border-radius: 8px;
    overflow: hidden;
  }

  .whereby-video-container {
    background: #000;
  }
}

.video-provider-buttons {
  .v-btn {
    min-width: auto;
  }
}

.video-controls {
  .v-btn {
    color: white !important;
  }
}
</style>
