const express = require('express')
const axios = require('axios')

const app = express()

// Middleware to parse JSON bodies
app.use(express.json())

// Whereby API configuration
const WHEREBY_API_KEY = 'langu-test'
const WHEREBY_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.1dZgX4paCsT4jgcW7ueteClMW8ZzlASc8j1LphFja8U'
const WHEREBY_API_BASE = 'https://api.whereby.dev/v1'

// Create a new Whereby room for a classroom session
app.post('/create-room', async (req, res) => {
  try {
    const { lessonId, teacherId, studentId, isRecurring = false } = req.body

    if (!lessonId) {
      return res.status(400).json({
        error: 'lessonId is required'
      })
    }

    // Generate a unique room name based on lesson details
    const roomName = `lesson-${lessonId}-${Date.now()}`
    
    // Calculate room duration (default 2 hours for lessons)
    const startDate = new Date()
    const endDate = new Date(startDate.getTime() + (2 * 60 * 60 * 1000)) // 2 hours from now

    // Whereby room creation payload
    const roomData = {
      roomName,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      fields: ['hostRoomUrl', 'roomUrl', 'meetingId'],
      // Enable all paid features
      roomMode: 'group', // Allows multiple participants
      isLocked: false, // Allow participants to join without waiting
      roomNamePrefix: '/langu-classroom-',
      roomNamePattern: 'uuid'
    }

    // Create room via Whereby API
    const response = await axios.post(`${WHEREBY_API_BASE}/meetings`, roomData, {
      headers: {
        'Authorization': `Bearer ${WHEREBY_TOKEN}`,
        'Content-Type': 'application/json'
      }
    })

    const roomInfo = response.data

    // Return room information
    res.json({
      success: true,
      room: {
        lessonId,
        roomName: roomInfo.roomName,
        hostRoomUrl: roomInfo.hostRoomUrl, // For teachers
        roomUrl: roomInfo.roomUrl, // For students
        meetingId: roomInfo.meetingId,
        startDate: roomInfo.startDate,
        endDate: roomInfo.endDate,
        createdAt: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error creating Whereby room:', error.response?.data || error.message)
    
    // Return error response
    res.status(500).json({
      error: 'Failed to create Whereby room',
      details: error.response?.data?.error || error.message
    })
  }
})

// Get room information
app.get('/room/:lessonId', async (req, res) => {
  try {
    const { lessonId } = req.params

    if (!lessonId) {
      return res.status(400).json({
        error: 'lessonId is required'
      })
    }

    // In a real implementation, you would fetch this from your database
    // For now, we'll return a placeholder response
    res.json({
      success: true,
      message: 'Room info endpoint - implement database lookup',
      lessonId
    })

  } catch (error) {
    console.error('Error fetching room info:', error.message)
    res.status(500).json({
      error: 'Failed to fetch room information',
      details: error.message
    })
  }
})

// Delete/end a room
app.delete('/room/:meetingId', async (req, res) => {
  try {
    const { meetingId } = req.params

    if (!meetingId) {
      return res.status(400).json({
        error: 'meetingId is required'
      })
    }

    // End the meeting via Whereby API
    const response = await axios.delete(`${WHEREBY_API_BASE}/meetings/${meetingId}`, {
      headers: {
        'Authorization': `Bearer ${WHEREBY_TOKEN}`
      }
    })

    res.json({
      success: true,
      message: 'Room ended successfully',
      meetingId
    })

  } catch (error) {
    console.error('Error ending Whereby room:', error.response?.data || error.message)
    res.status(500).json({
      error: 'Failed to end room',
      details: error.response?.data?.error || error.message
    })
  }
})

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'whereby-api',
    timestamp: new Date().toISOString()
  })
})

module.exports = app
