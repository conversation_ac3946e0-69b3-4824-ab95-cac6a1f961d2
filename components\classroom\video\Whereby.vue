<template>
  <div class="whereby-stream">
    <classroom-container :asset="file" :hover-enabled="false">
      <div
        id="video-window"
        :class="[
          'whereby-component cursor-before-grab',
          { 'video-window--is-fullscreen': settings.isFullscreenEnabled },
        ]"
      >
        <div id="whereby-video-container" class="whereby-video-container"></div>

        <video-actions
          :is-joined="isJoined"
          :settings="{
            ...settings,
            isMuted,
            isVideoEnabled,
            isHandRaised,
            isChatEnabled,
            isParticipantsEnabled,
          }"
          :is-screen-share-disabled="
            isRemoteScreenShareEnabled || screenSharingNotSupported
          "
          :type="file.asset.type"
          @switch-video-player="switchVideoPlayer"
          @toggle-video="toggleVideo"
          @toggle-audio="toggleAudio"
          @toggle-full-screen="toggleFullScreen"
          @toggle-screen-share="toggleScreenShare"
          @toggle-hand-raise="toggleHandRaise"
          @toggle-chat="toggleChat"
          @toggle-participants="toggleParticipants"
        ></video-actions>
      </div>
    </classroom-container>

    <classroom-container
      v-show="isLocalScreenShareEnabled || isRemoteScreenShareEnabled"
      :asset="screenShareAsset"
      :hover-enabled="false"
    >
      <div class="whereby-component screenshare-component cursor-before-grab">
        <div class="user-name">
          <template v-if="isLocalScreenShareEnabled">
            {{ $t('my_screen') }}
          </template>
          <template v-if="isRemoteScreenShareEnabled">
            {{
              $t('classroom_user_screen', {
                username: zoomOtherAsset.asset.username,
              })
            }}
          </template>
        </div>
        <div
          id="whereby-screenshare-placeholder"
          class="screenshare-stream"
        ></div>
      </div>
    </classroom-container>
  </div>
</template>

<script>
import { switchFullScreen } from '~/helpers'
import { isSupportedScreenShare } from '~/helpers/check_device'

import ClassroomContainer from '~/components/classroom/ClassroomContainer'
import VideoActions from '~/components/classroom/video/VideoActions'

export default {
  name: 'Whereby',
  components: {
    ClassroomContainer,
    VideoActions,
  },
  props: {
    file: {
      type: Object,
      required: true,
    },
    screenShareAsset: {
      type: Object,
      required: true,
    },
    zoomOtherAsset: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    room: null,
    localStreamContainer: null,
    remoteStreamContainer: null,
    screenShareStreamContainer: null,
    screenShareTrack: null,
    isLocalScreenShareEnabled: false,
    isRemoteScreenShareEnabled: false,
    screenSharingNotSupported: !isSupportedScreenShare(),
    isJoined: false,
    settings: {
      isScreenShareEnabled: false,
      isFullscreenEnabled: false,
    },
    wherebyRoom: null,
    wherebyClient: null,
    // New Whereby paid features
    isHandRaised: false,
    isChatEnabled: false,
    isParticipantsEnabled: false,
    currentRole: 'participant', // Will be determined based on classroom role
  }),
  computed: {
    wherebyApiKey() {
      return 'langu-test'
    },
    wherebyToken() {
      return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.1dZgX4paCsT4jgcW7ueteClMW8ZzlASc8j1LphFja8U'
    },
    wherebyRoomName() {
      return (
        this.$store.getters['classroom/twilioRoomName'] ||
        `lesson-${this.file.lessonId}`
      )
    },
    role() {
      return this.$store.getters['classroom/role']
    },
    isVideoEnabled() {
      return this.file.asset?.settings?.[this.role]?.isVideoEnabled ?? true
    },
    isMuted() {
      return this.file.asset?.settings?.[this.role]?.isMuted ?? false
    },
    maxIndex() {
      return this.$store.state.classroom.maxIndex
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.localStreamContainer = document.getElementById(
        'whereby-video-container'
      )
      this.screenShareStreamContainer = document.getElementById(
        'whereby-screenshare-placeholder'
      )

      this.initializeWhereby()
    })

    window.addEventListener('beforeunload', this.closeStream)
    window.addEventListener('pagehide', this.closeStream)

    document.addEventListener('fullscreenchange', this.fullscreenChangeHandler)
  },
  beforeDestroy() {
    document.removeEventListener(
      'fullscreenchange',
      this.fullscreenChangeHandler
    )
    this.closeStream()
  },
  methods: {
    initializeWhereby() {
      try {
        // First, try to create a room using Whereby API
        const roomUrl = this.createWherebyRoom()

        if (!roomUrl) {
          // eslint-disable-next-line no-console
          console.error('Failed to create Whereby room')
          this.switchVideoPlayer('tokbox')
          return
        }

        // Create iframe for Whereby embedded with all paid features
        const iframe = document.createElement('iframe')

        // Build embed parameters with all paid features enabled
        const embedParams = new URLSearchParams({
          embed: '',
          displayName: this.$store.getters['classroom/userName'] || 'User',
          audio: !this.isMuted ? 'on' : 'off',
          video: this.isVideoEnabled ? 'on' : 'off',
          // Enable all paid features
          chat: 'on', // Enable chat
          people: 'on', // Enable participants panel
          screenshare: 'on', // Enable screen sharing
          reactions: 'on', // Enable reactions
          handRaise: 'on', // Enable hand raising
          leaveButton: 'on', // Show leave button
          background: 'on', // Enable background effects
          recording: 'on', // Enable recording (if available)
          breakoutRooms: 'on', // Enable breakout rooms (if available)
          whiteboard: 'on', // Enable whiteboard (if available)
          minimal: 'false', // Full interface
        })

        // Determine if we need to add & or ? for parameters
        const separator = roomUrl.includes('?') ? '&' : '?'
        iframe.src = `${roomUrl}${separator}${embedParams.toString()}`

        iframe.style.width = '100%'
        iframe.style.height = '100%'
        iframe.style.border = 'none'
        iframe.style.borderRadius = '8px'
        iframe.allow =
          'camera; microphone; fullscreen; display-capture; autoplay'
        iframe.allowFullscreen = true

        // Clear container and add iframe
        this.localStreamContainer.innerHTML = ''
        this.localStreamContainer.appendChild(iframe)

        // Set up message listener for iframe communication
        window.addEventListener('message', this.handleWherebyMessage)

        // Mark as joined after a short delay
        setTimeout(() => {
          this.isJoined = true
          // eslint-disable-next-line no-console
          console.log('Whereby room initialized')
        }, 1000)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to initialize Whereby:', error)
        this.switchVideoPlayer('tokbox')
      }
    },

    createWherebyRoom() {
      try {
        // Determine role based on classroom role
        const isTeacher = this.role === 'teacher'
        this.currentRole = isTeacher ? 'host' : 'participant'

        // Use the same room URLs as in meet page
        let roomUrl
        if (isTeacher) {
          // Teacher gets host access with roomKey
          roomUrl =
            'https://langu.whereby.com/rwrre2ae7c7d2-8906-4ceb-90fd-f4571e5e75e6?roomKey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************.u1vYAr2HS_axqTBsyHAvdoztlZHcL09-9_9gkf4b548'
        } else {
          // Student gets participant access
          roomUrl =
            'https://langu.whereby.com/rwrre2ae7c7d2-8906-4ceb-90fd-f4571e5e75e6'
        }

        // eslint-disable-next-line no-console
        console.log('Using Whereby room:', roomUrl, 'Role:', this.currentRole)

        return roomUrl
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to create Whereby room:', error)

        // Fallback to participant room
        return 'https://langu.whereby.com/rwrre2ae7c7d2-8906-4ceb-90fd-f4571e5e75e6'
      }
    },

    handleWherebyMessage(event) {
      // Handle messages from Whereby iframe
      if (event.origin !== 'https://langu-test.whereby.com') {
        return
      }

      try {
        const data = JSON.parse(event.data)

        switch (data.type) {
          case 'participant_join':
            // eslint-disable-next-line no-console
            console.log('Participant joined:', data.participant)
            break
          case 'participant_leave':
            // eslint-disable-next-line no-console
            console.log('Participant left:', data.participant)
            break
          case 'screenshare_start':
            this.isRemoteScreenShareEnabled = true
            break
          case 'screenshare_stop':
            this.isRemoteScreenShareEnabled = false
            break
          case 'error':
            // eslint-disable-next-line no-console
            console.error('Whereby error:', data.error)
            this.handleMediaError(data.error)
            break
        }
      } catch (e) {
        // Ignore non-JSON messages
      }
    },
    handleMediaError(error) {
      // eslint-disable-next-line no-console
      console.error('Whereby media error:', error)
      alert(
        'Could not connect to video call. Please check your camera and microphone permissions.'
      )
    },
    toggleVideo() {
      this.updateData(this.file.id, {
        settings: {
          ...this.file.asset.settings,
          [this.role]: {
            isVideoEnabled: !this.isVideoEnabled,
            isMuted: this.isMuted,
          },
        },
      })

      // Note: With iframe approach, video/audio controls are handled within the Whereby interface
      // The iframe will respect the initial settings passed in the URL parameters
      // eslint-disable-next-line no-console
      console.log('Video toggled:', !this.isVideoEnabled)
    },
    toggleAudio() {
      this.updateData(this.file.id, {
        settings: {
          ...this.file.asset.settings,
          [this.role]: {
            isVideoEnabled: this.isVideoEnabled,
            isMuted: !this.isMuted,
          },
        },
      })

      // Note: With iframe approach, video/audio controls are handled within the Whereby interface
      // The iframe will respect the initial settings passed in the URL parameters
      // eslint-disable-next-line no-console
      console.log('Audio toggled:', !this.isMuted)
    },
    toggleFullScreen() {
      this.settings.isFullscreenEnabled = !this.settings.isFullscreenEnabled
      switchFullScreen(this.settings.isFullscreenEnabled)
    },
    toggleScreenShare() {
      this.settings.isScreenShareEnabled = !this.settings.isScreenShareEnabled

      if (this.settings.isScreenShareEnabled) {
        this.isLocalScreenShareEnabled = false
        this.screenShareStreamContainer.innerHTML = ''
      } else {
        // Note: With iframe approach, screen sharing is handled within the Whereby interface
        // Users can use the screen share button within the Whereby room
        this.updateData(this.screenShareAsset.id, {
          index: this.maxIndex + 1,
        })
        this.isLocalScreenShareEnabled = true
      }
    },

    toggleHandRaise() {
      this.isHandRaised = !this.isHandRaised
      // eslint-disable-next-line no-console
      console.log('Hand raise toggled:', this.isHandRaised)
      // Note: Actual hand raising is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },

    toggleChat() {
      this.isChatEnabled = !this.isChatEnabled
      // eslint-disable-next-line no-console
      console.log('Chat toggled:', this.isChatEnabled)
      // Note: Actual chat toggle is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },

    toggleParticipants() {
      this.isParticipantsEnabled = !this.isParticipantsEnabled
      // eslint-disable-next-line no-console
      console.log('Participants panel toggled:', this.isParticipantsEnabled)
      // Note: Actual participants panel is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },
    switchVideoPlayer(type) {
      this.$store.dispatch('classroom/deleteAsset', this.file)
      this.$store.dispatch('classroom/createAsset', {
        ...this.file.asset,
        type,
      })
    },
    fullscreenChangeHandler() {
      if (!document.fullscreenElement) {
        this.settings.isFullscreenEnabled = false
      }
    },
    updateData(id, asset) {
      this.$store.commit('classroom/moveAsset', {
        id,
        asset,
      })
      this.$store.dispatch('classroom/moveAsset', {
        id,
        lessonId: this.file.lessonId,
        asset,
      })
    },
    closeStream() {
      // Clean up iframe and event listeners
      if (this.localStreamContainer) {
        this.localStreamContainer.innerHTML = ''
      }
      window.removeEventListener('message', this.handleWherebyMessage)
    },
  },
}
</script>

<style lang="scss" scoped>
.whereby-stream {
  .whereby-component {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;

    .whereby-video-container {
      width: 100%;
      height: 100%;
      min-height: 200px;
    }

    &.screenshare-component {
      .user-name {
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 10;
      }

      .screenshare-stream {
        width: 100%;
        height: 100%;
        min-height: 200px;
      }
    }

    &.video-window--is-fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw !important;
      height: 100vh !important;
      z-index: 9999;
      border-radius: 0;
    }
  }
}
</style>
