<template>
  <div class="whereby-stream">
    <classroom-container :asset="file" :hover-enabled="false">
      <div
        id="video-window"
        :class="[
          'whereby-component cursor-before-grab',
          { 'video-window--is-fullscreen': settings.isFullscreenEnabled },
        ]"
      >
        <div id="whereby-video-container" class="whereby-video-container"></div>

        <video-actions
          :is-joined="isJoined"
          :settings="{
            ...settings,
            isMuted,
            isVideoEnabled,
            isHandRaised,
            isChatEnabled,
            isParticipantsEnabled,
          }"
          :is-screen-share-disabled="
            isRemoteScreenShareEnabled || screenSharingNotSupported
          "
          :type="file.asset.type"
          @switch-video-player="switchVideoPlayer"
          @toggle-video="toggleVideo"
          @toggle-audio="toggleAudio"
          @toggle-full-screen="toggleFullScreen"
          @toggle-screen-share="toggleScreenShare"
          @toggle-hand-raise="toggleHandRaise"
          @toggle-chat="toggleChat"
          @toggle-participants="toggleParticipants"
        ></video-actions>
      </div>
    </classroom-container>

    <classroom-container
      v-show="isLocalScreenShareEnabled || isRemoteScreenShareEnabled"
      :asset="screenShareAsset"
      :hover-enabled="false"
    >
      <div class="whereby-component screenshare-component cursor-before-grab">
        <div class="user-name">
          <template v-if="isLocalScreenShareEnabled">
            {{ $t('my_screen') }}
          </template>
          <template v-if="isRemoteScreenShareEnabled">
            {{
              $t('classroom_user_screen', {
                username: zoomOtherAsset.asset.username,
              })
            }}
          </template>
        </div>
        <div
          id="whereby-screenshare-placeholder"
          class="screenshare-stream"
        ></div>
      </div>
    </classroom-container>
  </div>
</template>

<script>
import { isSupportedScreenShare } from '~/helpers/check_device'
import { createWherebyRoom } from '~/helpers/whereby-api'

import ClassroomContainer from '~/components/classroom/ClassroomContainer'

export default {
  name: 'Whereby',
  components: {
    ClassroomContainer,
  },
  props: {
    file: {
      type: Object,
      required: true,
    },
    screenShareAsset: {
      type: Object,
      required: true,
    },
    zoomOtherAsset: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    localStreamContainer: null,
    screenShareStreamContainer: null,
    isLocalScreenShareEnabled: false,
    isRemoteScreenShareEnabled: false,
    screenSharingNotSupported: !isSupportedScreenShare(),
    isJoined: false,
    currentRole: 'participant', // Will be determined based on classroom role
    wherebyRoom: null, // Store the created room information
    isCreatingRoom: false, // Loading state for room creation
  }),
  computed: {
    lessonId() {
      return this.file.lessonId || this.$store.state.classroom.lessonId
    },
    teacherId() {
      return this.$store.state.classroom.teacherId
    },
    studentId() {
      return this.$store.state.classroom.studentId
    },
    role() {
      return this.$store.getters['classroom/role']
    },
    isVideoEnabled() {
      return this.file.asset?.settings?.[this.role]?.isVideoEnabled ?? true
    },
    isMuted() {
      return this.file.asset?.settings?.[this.role]?.isMuted ?? false
    },
    maxIndex() {
      return this.$store.state.classroom.maxIndex
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.localStreamContainer = document.getElementById(
        'whereby-video-container'
      )
      this.screenShareStreamContainer = document.getElementById(
        'whereby-screenshare-placeholder'
      )

      this.initializeWhereby()
    })

    window.addEventListener('beforeunload', this.closeStream)
    window.addEventListener('pagehide', this.closeStream)

    document.addEventListener('fullscreenchange', this.fullscreenChangeHandler)
  },
  beforeDestroy() {
    document.removeEventListener(
      'fullscreenchange',
      this.fullscreenChangeHandler
    )
    this.closeStream()
  },
  methods: {
    async initializeWhereby() {
      try {
        // Check if container exists
        if (!this.localStreamContainer) {
          // eslint-disable-next-line no-console
          console.error('localStreamContainer is null')
          return
        }

        // Determine role based on classroom role
        const isTeacher = this.role === 'teacher'
        this.currentRole = isTeacher ? 'host' : 'participant'

        // Show loading state
        this.isCreatingRoom = true
        this.localStreamContainer.innerHTML = `
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">
            <div style="margin-bottom: 20px;">
              <h3 style="color: #333; margin-bottom: 10px;">Creating Whereby Room...</h3>
              <p style="color: #666; margin-bottom: 20px;">Please wait while we set up your video call</p>
            </div>
            <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #5E72E4; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <style>
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            </style>
          </div>
        `

        // Create or get existing room
        if (!this.wherebyRoom) {
          this.wherebyRoom = await createWherebyRoom({
            lessonId: this.lessonId,
            teacherId: this.teacherId,
            studentId: this.studentId,
            isRecurring: false,
          })
        }

        // Hide loading state
        this.isCreatingRoom = false

        // Create iframe for inline embedding like options A and B
        const iframe = document.createElement('iframe')

        // Determine the URL based on role
        const baseUrl =
          this.currentRole === 'host'
            ? this.wherebyRoom.hostRoomUrl
            : this.wherebyRoom.roomUrl

        // Build embed parameters with all paid features enabled
        const embedParams = new URLSearchParams({
          embed: '',
          displayName: this.$store.getters['classroom/userName'] || 'User',
          audio: !this.isMuted ? 'on' : 'off',
          video: this.isVideoEnabled ? 'on' : 'off',
          // Enable all paid features
          chat: 'on',
          people: 'on',
          screenshare: 'on',
          reactions: 'on',
          handRaise: 'on',
          leaveButton: 'on',
          background: 'on',
          recording: 'on',
          breakoutRooms: 'on',
          whiteboard: 'on',
          minimal: 'false',
        })

        // Determine if we need to add & or ? for parameters
        const separator = baseUrl.includes('?') ? '&' : '?'
        iframe.src = `${baseUrl}${separator}${embedParams.toString()}`

        iframe.style.width = '100%'
        iframe.style.height = '100%'
        iframe.style.border = 'none'
        iframe.style.borderRadius = '8px'
        iframe.allow =
          'camera; microphone; fullscreen; display-capture; autoplay'
        iframe.allowFullscreen = true

        // Clear container and add iframe
        this.localStreamContainer.innerHTML = ''
        this.localStreamContainer.appendChild(iframe)

        // Set up message listener for iframe communication
        window.addEventListener('message', this.handleWherebyMessage)

        // Mark as joined after a short delay
        setTimeout(() => {
          this.isJoined = true
          // eslint-disable-next-line no-console
          console.log('Whereby room initialized inline:', this.wherebyRoom)
        }, 1000)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to initialize Whereby:', error)

        this.isCreatingRoom = false

        // Check if container exists before setting innerHTML
        if (this.localStreamContainer) {
          this.localStreamContainer.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">
              <div style="margin-bottom: 20px;">
                <h3 style="color: #d32f2f; margin-bottom: 10px;">Failed to Create Room</h3>
                <p style="color: #666; margin-bottom: 20px;">Unable to create Whereby room. Please try switching to another video provider.</p>
              </div>
              <button
                onclick="this.parentElement.parentElement.parentElement.querySelector('.toolbar-button-item').click()"
                style="
                  background: #5E72E4;
                  color: white;
                  border: none;
                  padding: 12px 20px;
                  border-radius: 6px;
                  font-size: 14px;
                  cursor: pointer;
                  font-weight: 600;
                "
              >
                Switch to Provider A
              </button>
            </div>
          `
        }
      }
    },

    handleWherebyMessage(event) {
      // Handle messages from Whereby iframe
      if (!event.origin.includes('whereby.com')) {
        return
      }

      try {
        const data = JSON.parse(event.data)

        switch (data.type) {
          case 'participant_join':
            // eslint-disable-next-line no-console
            console.log('Participant joined:', data.participant)
            break
          case 'participant_leave':
            // eslint-disable-next-line no-console
            console.log('Participant left:', data.participant)
            break
          case 'screenshare_start':
            this.isRemoteScreenShareEnabled = true
            break
          case 'screenshare_stop':
            this.isRemoteScreenShareEnabled = false
            break
          case 'error':
            // eslint-disable-next-line no-console
            console.error('Whereby error:', data.error)
            this.handleMediaError(data.error)
            break
        }
      } catch (e) {
        // Ignore non-JSON messages
      }
    },
    handleMediaError(error) {
      // eslint-disable-next-line no-console
      console.error('Whereby media error:', error)
      alert(
        'Could not connect to video call. Please check your camera and microphone permissions.'
      )
    },
    toggleVideo() {
      this.updateData(this.file.id, {
        settings: {
          ...this.file.asset.settings,
          [this.role]: {
            isVideoEnabled: !this.isVideoEnabled,
            isMuted: this.isMuted,
          },
        },
      })

      // Note: With iframe approach, video/audio controls are handled within the Whereby interface
      // The iframe will respect the initial settings passed in the URL parameters
      // eslint-disable-next-line no-console
      console.log('Video toggled:', !this.isVideoEnabled)
    },
    toggleAudio() {
      this.updateData(this.file.id, {
        settings: {
          ...this.file.asset.settings,
          [this.role]: {
            isVideoEnabled: this.isVideoEnabled,
            isMuted: !this.isMuted,
          },
        },
      })

      // Note: With iframe approach, video/audio controls are handled within the Whereby interface
      // The iframe will respect the initial settings passed in the URL parameters
      // eslint-disable-next-line no-console
      console.log('Audio toggled:', !this.isMuted)
    },
    toggleFullScreen() {
      this.settings.isFullscreenEnabled = !this.settings.isFullscreenEnabled
      this.switchFullScreen(this.settings.isFullscreenEnabled)
    },

    switchFullScreen(enable) {
      const elem = document.getElementById('video-window')
      if (!elem) return
      if (enable) {
        if (elem.requestFullscreen) {
          elem.requestFullscreen()
        } else if (elem.mozRequestFullScreen) {
          elem.mozRequestFullScreen()
        } else if (elem.webkitRequestFullscreen) {
          elem.webkitRequestFullscreen()
        } else if (elem.msRequestFullscreen) {
          elem.msRequestFullscreen()
        }
      } else if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    },
    toggleScreenShare() {
      this.settings.isScreenShareEnabled = !this.settings.isScreenShareEnabled

      if (this.settings.isScreenShareEnabled) {
        this.isLocalScreenShareEnabled = false
        this.screenShareStreamContainer.innerHTML = ''
      } else {
        // Note: With iframe approach, screen sharing is handled within the Whereby interface
        // Users can use the screen share button within the Whereby room
        this.updateData(this.screenShareAsset.id, {
          index: this.maxIndex + 1,
        })
        this.isLocalScreenShareEnabled = true
      }
    },

    toggleHandRaise() {
      this.isHandRaised = !this.isHandRaised
      // eslint-disable-next-line no-console
      console.log('Hand raise toggled:', this.isHandRaised)
      // Note: Actual hand raising is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },

    toggleChat() {
      this.isChatEnabled = !this.isChatEnabled
      // eslint-disable-next-line no-console
      console.log('Chat toggled:', this.isChatEnabled)
      // Note: Actual chat toggle is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },

    toggleParticipants() {
      this.isParticipantsEnabled = !this.isParticipantsEnabled
      // eslint-disable-next-line no-console
      console.log('Participants panel toggled:', this.isParticipantsEnabled)
      // Note: Actual participants panel is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },
    switchVideoPlayer(type) {
      this.$store.dispatch('classroom/deleteAsset', this.file)
      this.$store.dispatch('classroom/createAsset', {
        ...this.file.asset,
        type,
      })
    },
    fullscreenChangeHandler() {
      if (!document.fullscreenElement) {
        this.settings.isFullscreenEnabled = false
      }
    },
    updateData(id, asset) {
      this.$store.commit('classroom/moveAsset', {
        id,
        asset,
      })
      this.$store.dispatch('classroom/moveAsset', {
        id,
        lessonId: this.file.lessonId,
        asset,
      })
    },
    closeStream() {
      // Clean up iframe and event listeners
      if (this.localStreamContainer) {
        this.localStreamContainer.innerHTML = ''
      }
      window.removeEventListener('message', this.handleWherebyMessage)
    },
  },
}
</script>

<style lang="scss" scoped>
.whereby-stream {
  .whereby-component {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;

    .whereby-video-container {
      width: 100%;
      height: 100%;
      min-height: 200px;
    }

    &.screenshare-component {
      .user-name {
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 10;
      }

      .screenshare-stream {
        width: 100%;
        height: 100%;
        min-height: 200px;
      }
    }

    &.video-window--is-fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw !important;
      height: 100vh !important;
      z-index: 9999;
      border-radius: 0;
    }
  }
}
</style>
